FROM node:lts AS build-stage
ARG NPM_REGISTRY=https://registry.npmmirror.com/
WORKDIR /app
COPY ["package.json", "package-lock.json", "./"]
RUN npm config set registry $NPM_REGISTRY && npm install
COPY . .
RUN npm run build

FROM nginx:latest AS prod-stage
RUN mkdir /app
COPY --from=build-stage /app/dist /app
COPY ciga.conf /ciga.conf
ENTRYPOINT ["/bin/bash", "-c", \
    "envsubst '$BACKEND_URL,$ROOT_DIR' < ciga.conf > /etc/nginx/conf.d/ciga.conf \
    && exec nginx -g 'daemon off;'"]
