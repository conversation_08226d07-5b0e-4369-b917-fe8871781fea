# data_analysis

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```

## Deployment

### With Docker

Firstly, add a `.env` file like the following example:

```
BACKEND_URL=http://<your-backend-address>:<your-backend-port>
ROOT_DIR=/app
```

Install docker and run the following:

```
docker build -t ciga:latest .
docker run -d -p 8888:8888 --env-file .env --name ciga-front ciga:latest
```
