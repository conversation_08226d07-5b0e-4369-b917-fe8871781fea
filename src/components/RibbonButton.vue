<template>
  <button
    class="ribbon-button"
    :class="[
      size,
      {
        'ribbon-button-active': active,
        'ribbon-button-disabled': disabled,
      },
    ]"
    :disabled="disabled"
    @click="handleClick"
  >
    <div class="ribbon-button-content">
      <!-- Icon -->
      <div v-if="icon || $slots.icon" class="ribbon-button-icon">
        <slot name="icon">
          <component :is="icon" v-if="typeof icon === 'object'" />
          <div v-else-if="typeof icon === 'string'" v-html="icon"></div>
        </slot>
      </div>

      <!-- Text -->
      <div v-if="text || $slots.default" class="ribbon-button-text">
        <slot>{{ text }}</slot>
      </div>

      <!-- Dropdown arrow -->
      <div v-if="dropdown" class="ribbon-button-dropdown">
        <svg width="8" height="8" viewBox="0 0 8 8" fill="currentColor">
          <path d="M0 2l4 4 4-4z" />
        </svg>
      </div>
    </div>
  </button>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  icon: {
    type: [String, Object],
    default: null,
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value),
  },
  active: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  dropdown: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['click'])

const handleClick = (event) => {
  if (!props.disabled) {
    emit('click', event)
  }
}
</script>

<style scoped>
.ribbon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  background: transparent;
  cursor: pointer;
  transition: all 0.15s ease;
  border-radius: 3px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  outline: none;
  box-shadow: none;
}

.ribbon-button:focus {
  box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
  border-color: #86b7fe;
}

.ribbon-button:hover:not(.ribbon-button-disabled) {
  background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
  border-color: #adb5bd;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ribbon-button:active:not(.ribbon-button-disabled) {
  background: linear-gradient(to bottom, #e9ecef 0%, #f8f9fa 100%);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  transform: translateY(1px);
}

.ribbon-button.ribbon-button-active {
  background: linear-gradient(to bottom, #cfe2ff 0%, #e7f1ff 100%);
  border-color: #6ea8fe;
  color: #0a58ca;
  box-shadow: inset 0 1px 2px rgba(13, 110, 253, 0.1);
}

.ribbon-button.ribbon-button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: transparent;
  box-shadow: none;
}

.ribbon-button-content {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
}

.ribbon-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ribbon-button-text {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  line-height: 1.3;
  color: inherit;
}

.ribbon-button-dropdown {
  display: flex;
  align-items: center;
  margin-left: 2px;
  opacity: 0.7;
}

/* Size variants */
.ribbon-button.small {
  min-height: 24px;
  min-width: 24px;
}

.ribbon-button.small .ribbon-button-content {
  padding: 2px 6px;
}

.ribbon-button.small .ribbon-button-text {
  font-size: 11px;
}

.ribbon-button.small .ribbon-button-icon {
  width: 14px;
  height: 14px;
}

.ribbon-button.medium {
  min-height: 32px;
  min-width: 32px;
}

.ribbon-button.medium .ribbon-button-content {
  padding: 4px 8px;
}

.ribbon-button.medium .ribbon-button-text {
  font-size: 12px;
}

.ribbon-button.medium .ribbon-button-icon {
  width: 18px;
  height: 18px;
}

.ribbon-button.large {
  min-height: 64px;
  min-width: 64px;
  flex-direction: column;
}

.ribbon-button.large .ribbon-button-content {
  flex-direction: column;
  padding: 8px 12px;
  gap: 6px;
  text-align: center;
}

.ribbon-button.large .ribbon-button-text {
  font-size: 11px;
  text-align: center;
  max-width: 72px;
  line-height: 1.2;
  word-wrap: break-word;
  hyphens: auto;
}

.ribbon-button.large .ribbon-button-icon {
  width: 32px;
  height: 32px;
}

/* Icon-only buttons */
.ribbon-button:not(.large)
  .ribbon-button-content:has(.ribbon-button-icon):not(:has(.ribbon-button-text)) {
  padding: 6px;
}

.ribbon-button.small
  .ribbon-button-content:has(.ribbon-button-icon):not(:has(.ribbon-button-text)) {
  padding: 4px;
}

/* Ensure proper spacing for grouped buttons */
.ribbon-button + .ribbon-button {
  margin-left: 2px;
}

/* Enhanced hover effects for different sizes */
.ribbon-button.large:hover:not(.ribbon-button-disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.ribbon-button.large:active:not(.ribbon-button-disabled) {
  transform: translateY(0);
}
</style>
