import { useStates } from '@/store/states'
import * as XLSX from 'xlsx'
import <PERSON> from 'papaparse'
import axios from 'axios'

/**
 * Export sheet data to CSV format
 * @param {Object} sheetData - Sheet data object with columns and rows
 * @param {string} sheetName - Name of the sheet for filename
 * @returns {Promise<void>}
 */
export async function exportSheetToCSV(sheetData, sheetName) {
  if (!sheetData || !sheetData.columns || !sheetData.rows) {
    throw new Error('Invalid sheet data')
  }

  try {
    // Prepare data for CSV export
    const csvData = sheetData.rows.map((row) => {
      const csvRow = {}
      sheetData.columns.forEach((col) => {
        csvRow[col.title || col.field] = row[col.field] || ''
      })
      return csvRow
    })

    // Convert to CSV using Papa Parse
    const csv = Papa.unparse(csvData)

    // Create blob and download
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `${sheetName || 'sheet'}_${timestamp}.csv`

    // Create download link and trigger download
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()

    // Cleanup
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    return filename
  } catch (error) {
    console.error('Export failed:', error)
    throw new Error(`导出失败: ${error.message}`)
  }
}

/**
 * Export sheet data to Excel format
 * @param {Object} sheetData - Sheet data object with columns and rows
 * @param {string} sheetName - Name of the sheet for filename
 * @returns {Promise<void>}
 */
export async function exportSheetToExcel(sheetData, sheetName) {
  if (!sheetData || !sheetData.columns || !sheetData.rows) {
    throw new Error('Invalid sheet data')
  }

  try {
    // Create workbook and worksheet
    const wb = XLSX.utils.book_new()

    // Prepare data for Excel export
    const wsData = [
      // Header row
      sheetData.columns.map((col) => col.title || col.field),
      // Data rows
      ...sheetData.rows.map((row) => sheetData.columns.map((col) => row[col.field] || '')),
    ]

    const ws = XLSX.utils.aoa_to_sheet(wsData)
    XLSX.utils.book_append_sheet(wb, ws, sheetName || 'Sheet1')

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `${sheetName || 'sheet'}_${timestamp}.xlsx`

    // Write and download
    XLSX.writeFile(wb, filename)

    return filename
  } catch (error) {
    console.error('Export failed:', error)
    throw new Error(`导出失败: ${error.message}`)
  }
}

/**
 * Upload file to the new upload_data API endpoint
 * @param {File} file - The file to upload
 * @param {string} taskId - Task ID for the upload
 * @returns {Promise<Object>} Upload result with table_id and table_data
 */
export async function uploadFileToAPI(file, taskId) {
  // Validate inputs
  if (!file) {
    throw new Error('文件不能为空')
  }
  if (!taskId) {
    throw new Error('任务ID不能为空')
  }

  // Check authentication token
  const token = localStorage.getItem('token')
  if (!token) {
    throw new Error('认证失败，请重新登录')
  }

  try {
    // Create FormData for multipart/form-data request
    const formData = new FormData()
    formData.append('file', file)
    formData.append('task_id', taskId)

    console.log('Uploading file:', {
      fileName: file.name,
      fileSize: file.size,
      taskId: taskId,
    })

    // Make API call to upload endpoint
    const response = await axios.post('/api/v1/config/upload_data', formData, {
      headers: {
        token: token,
        'Content-Type': 'multipart/form-data',
      },
      timeout: 20000, // 60 second timeout for file uploads
    })

    console.log('Upload API response:', response.data)

    // Handle API response
    const responseData = response.data

    // Validate response structure
    if (!responseData || typeof responseData !== 'object') {
      throw new Error('服务器响应格式异常')
    }

    // Check for API errors
    if (responseData.error_code !== 0) {
      let errorMessage = '文件上传失败'

      switch (responseData.error_code) {
        case 301:
          errorMessage = '认证失败，请重新登录'
          break
        case 302:
          errorMessage = '请求参数缺失，请检查必填项'
          break
        default:
          errorMessage = responseData.message || '未知错误'
      }

      throw new Error(errorMessage)
    }

    // Validate response data
    if (!responseData.data) {
      throw new Error('服务器未返回数据')
    }

    const { table_id, table_data } = responseData.data

    if (!table_data || typeof table_data !== 'object') {
      throw new Error('服务器返回的数据格式异常')
    }

    // Return successful response data
    return {
      success: true,
      table_id: table_id,
      table_data: table_data,
      fileName: file.name,
    }
  } catch (error) {
    console.error('File upload API error:', error)

    // Handle different error types
    if (error.response) {
      // Server responded with error status
      const status = error.response.status
      const data = error.response.data

      if (status === 401) {
        throw new Error('认证失败，请重新登录')
      } else if (status === 400) {
        throw new Error('请求参数错误')
      } else if (status === 413) {
        throw new Error('文件大小超过服务器限制')
      } else if (status === 415) {
        throw new Error('不支持的文件格式')
      } else if (data && data.message) {
        throw new Error(data.message)
      } else {
        throw new Error(`服务器错误 (${status})`)
      }
    } else if (error.request) {
      // Network error
      if (error.code === 'ECONNABORTED') {
        throw new Error('上传超时，请检查网络连接或稍后重试')
      } else {
        throw new Error('网络连接失败，请检查网络')
      }
    } else {
      // Other errors (including validation errors from above)
      throw new Error(error.message || '文件上传失败')
    }
  }
}

/**
 * Generate a task ID for file upload
 * Uses existing taskName or creates a unique identifier
 * @returns {string} Task ID
 */
export function generateTaskId() {
  const statesStore = useStates()
  const taskName = statesStore.taskName || 'tmp'

  // Create a unique task ID combining task name and timestamp
  const timestamp = Date.now()
  const taskId = `${taskName}_${timestamp}`

  return taskId
}

/**
 * Store uploaded file data in centralized state following established patterns
 * @param {string} table_id - Table ID from API response
 * @param {Object} table_data - Table data from API response (column-based format)
 * @param {string} fileName - Original file name
 * @returns {Promise<void>}
 */
export async function storeUploadedData(table_id, table_data, fileName) {
  const statesStore = useStates()
  const tableState = statesStore.states.find((s) => s.componentName === 'table')
  if (!tableState || !table_data || typeof table_data !== 'object') {
    throw new Error('Invalid table state or data')
  }

  try {
    // Transform table_data from column-based to row-based format (following existing pattern)
    const columnNames = Object.keys(table_data)
    const rowCount = columnNames.length > 0 ? table_data[columnNames[0]]?.length || 0 : 0

    // Build columns metadata
    const columns = columnNames.map((columnName) => ({
      field: columnName,
      title: columnName,
      dtype: 'unknown', // API doesn't provide dtype info yet
      ftype: 'unknown', // API doesn't provide ftype info yet
      missing_count: 0,
      outlier_count: 0,
      unique_values: [],
    }))

    // Transform to row-based format
    const rows = []
    for (let i = 0; i < rowCount; i++) {
      const row = {}
      columnNames.forEach((columnName) => {
        // Explicitly handle zero values - don't use || null which would convert 0 to null
        const value = table_data[columnName]?.[i]
        row[columnName] = value !== undefined ? value : null
      })
      rows.push(row)
    }

    // Store in Pinia with enhanced metadata (following existing pattern)
    const tableName = table_id || `uploaded_${Date.now()}`
    const newSheetData = { ...(tableState.sheetData || {}) }

    newSheetData[tableName] = {
      name: fileName.replace(/\.[^.]+$/, ''), // Remove file extension
      columns,
      rows,
      table_id,
      table_data, // Store original column-based data for reference
      uploadTimestamp: new Date().toISOString(),
      source: 'api_upload', // Mark as API upload vs local import
    }

    statesStore.updateComponent(tableState.componentId, {
      ...tableState,
      activeSheet: tableName,
      sheetData: newSheetData,
    })

    console.log('Uploaded data stored successfully:', {
      table_id,
      tableName,
      columns: columns.length,
      rows: rows.length,
    })
  } catch (error) {
    console.error('Failed to store uploaded data:', error)
    throw new Error(`数据存储失败: ${error.message}`)
  }
}

export async function importAndAddSheet(file) {
  const statesStore = useStates()
  const tableState = statesStore.states.find((s) => s.componentName === 'table')
  if (!tableState) return

  let columns = []
  let rows = []
  if (file.name.endsWith('.csv')) {
    // Parse CSV
    const text = await file.text()
    const result = Papa.parse(text, { header: true })
    columns = result.meta.fields.map((field) => ({ field, title: field }))
    rows = result.data.filter((row) => Object.values(row).some((v) => v !== undefined && v !== ''))
  } else {
    // Parse Excel
    const data = await file.arrayBuffer()
    const workbook = XLSX.read(data, { type: 'array' })
    const wsname = workbook.SheetNames[0]
    const ws = workbook.Sheets[wsname]
    const json = XLSX.utils.sheet_to_json(ws, { header: 1 })
    if (json.length > 0) {
      columns = json[0].map((field) => ({ field, title: field }))
      rows = json.slice(1).map((row) => {
        const obj = {}
        columns.forEach((col, i) => {
          obj[col.field] = row[i]
        })
        return obj
      })
    }
  }
  if (!columns.length || !rows.length) return
  const idx = Object.keys(tableState.sheetData || {}).length + 1
  const key = `sheet${Date.now()}`
  const newSheetData = { ...(tableState.sheetData || {}) }
  newSheetData[key] = {
    name: file.name.replace(/\.[^.]+$/, ''),
    columns,
    rows,
  }
  statesStore.updateComponent(tableState.componentId, {
    ...tableState,
    sheetData: newSheetData,
    activeSheet: key,
  })
}
