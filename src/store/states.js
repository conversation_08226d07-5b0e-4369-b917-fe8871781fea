import { computed, ref } from 'vue'
import { defineStore } from 'pinia'
import { v4 as uuidv4 } from 'uuid'

const getDefaultStates = () => {
  return [
    {
      componentId: uuidv4(),
      componentName: '目标指标',
      indices: [],
    },
    {
      componentId: uuidv4(),
      componentName: '目标维度',
      dimensions: [],
    },
    {
      componentId: uuidv4(),
      componentName: '粒度',
      granularity: '',
      granularityId: '',
      granularityName: '',
    },
    {
      componentId: uuidv4(),
      componentName: '日期',
      dates: [],
    },
    {
      componentId: uuidv4(),
      componentName: 'trendAnalysis',
      selectedTrends: [],
      results: null,
    },
    {
      componentId: uuidv4(),
      componentName: 'corrAnalysis',
      selectedObjects: [],
      results: null,
    },
    {
      componentId: uuidv4(),
      componentName: 'table',
      activeSheet: '',
      sheetData: [],
      semanticsConfig: {},
    },
    {
      componentId: uuidv4(),
      componentName: 'dashboardCharts',
      charts: [],
    },
  ]
}

export const useStates = defineStore('states', () => {
  const pageName = ref('')
  const title = ref('')
  const taskName = ref('tmp') // Default task name for guided analysis
  const taskId = ref('') // Task ID from create_task API response
  const defaultStates = getDefaultStates()
  const stateIds = ref(new Set(defaultStates.map((state) => state.componentId)))
  const states = ref(defaultStates)
  const chatMessages = ref([])
  const username = ref('')
  const userRole = ref('') // 'admin' or ''
  // const authToken = ref('a1234567890123456789012345678901234567890')
  const tokenExpireTime = ref('')
  const userInfo = ref(null)
  const availableIndicators = ref([])
  const availableDimensions = ref([])

  // Category management for hierarchical selection
  const availableCategories = ref([])
  const selectedCategories = ref({
    indicators: {
      category_lv_1: '',
      category_lv_2: '',
      category_lv_3: '',
    },
    dimensions: {
      category_lv_1: '',
      category_lv_2: '',
      category_lv_3: '',
    },
  })

  // System error state for global error mask
  const systemError = ref({
    show: false,
    title: '',
    message: '',
    errorCode: null,
    retryAction: null,
    timestamp: null,
  })

  const getStates = computed(() => {
    return {
      pageName: pageName.value,
      title: title.value,
      timestamp: new Date().getTime(),
      states: states.value,
    }
  })

  function updateComponent(componentId, newStates) {
    this.$patch((s) => {
      if (!s.stateIds.has(componentId)) {
        s.stateIds.add(componentId)
        s.states.push({ componentId, ...newStates })
      } else {
        s.states.forEach((state) => {
          if (state.componentId === componentId) {
            Object.assign(state, newStates)
          }
        })
      }
    })
  }

  function deleteComponent(componentId) {
    this.$patch((s) => {
      s.stateIds.delete(componentId)
      s.states = s.states.filter((state) => state.componentId !== componentId)
    })
  }

  function setChatMessages(msgs) {
    chatMessages.value = msgs
  }

  function appendChatMessage(msg) {
    chatMessages.value.push(msg)
  }

  function clearChatMessages() {
    chatMessages.value = []
  }

  function setUsername(name) {
    username.value = name
  }

  function clearUsername() {
    username.value = ''
  }

  function setUserRole(role) {
    userRole.value = role
  }

  function clearUserRole() {
    userRole.value = ''
  }

  function setAuthToken(token) {
    // authToken.value = token
    localStorage.setItem('token', token)
  }

  function clearAuthToken() {
    // authToken.value = ''
    localStorage.removeItem('token')
  }

  function setTokenExpireTime(expireTime) {
    tokenExpireTime.value = expireTime
  }

  function clearTokenExpireTime() {
    tokenExpireTime.value = ''
  }

  function setUserInfo(info) {
    userInfo.value = info
  }

  function clearUserInfo() {
    userInfo.value = null
  }

  function clearAllAuthData() {
    clearUsername()
    clearUserRole()
    clearAuthToken()
    clearTokenExpireTime()
    clearUserInfo()
    // Clear localStorage as well
    localStorage.removeItem('auth_token')
    localStorage.removeItem('token_expire_time')
    localStorage.removeItem('user_info')
  }

  function setAvailableIndicators(indicators) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.availableIndicators = indicators
    })
  }

  function clearAvailableIndicators() {
    availableIndicators.value = []
  }

  function setAvailableDimensions(dimensions) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.availableDimensions = dimensions
    })
  }

  function clearAvailableDimensions() {
    availableDimensions.value = []
  }

  // Category management functions
  function setAvailableCategories(categories) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.availableCategories = categories
    })
  }

  function clearAvailableCategories() {
    availableCategories.value = []
  }

  function setSelectedCategories(type, categoryData) {
    // type: 'indicators' or 'dimensions'
    // categoryData: { category_lv_1, category_lv_2, category_lv_3 }
    this.$patch((s) => {
      s.selectedCategories[type] = { ...s.selectedCategories[type], ...categoryData }
    })
  }

  function clearSelectedCategories(type) {
    // type: 'indicators' or 'dimensions', or undefined to clear all
    this.$patch((s) => {
      if (type) {
        s.selectedCategories[type] = {
          category_lv_1: '',
          category_lv_2: '',
          category_lv_3: '',
        }
      } else {
        s.selectedCategories = {
          indicators: {
            category_lv_1: '',
            category_lv_2: '',
            category_lv_3: '',
          },
          dimensions: {
            category_lv_1: '',
            category_lv_2: '',
            category_lv_3: '',
          },
        }
      }
    })
  }

  // Task name management functions
  function setTaskName(name) {
    taskName.value = name || 'tmp'
  }

  function clearTaskName() {
    taskName.value = 'tmp'
  }

  // Task ID management functions
  function setTaskId(id) {
    taskId.value = id || ''
  }

  function clearTaskId() {
    taskId.value = ''
  }

  // System error management functions
  function showSystemError(title, message, errorCode = null, retryAction = null) {
    systemError.value = {
      show: true,
      title,
      message,
      errorCode,
      retryAction,
      timestamp: new Date().getTime(),
    }
  }

  function hideSystemError() {
    systemError.value = {
      show: false,
      title: '',
      message: '',
      errorCode: null,
      retryAction: null,
      timestamp: null,
    }
  }

  function clearSystemError() {
    hideSystemError()
  }

  // Table data management functions
  function clearSheetData(sheetKey) {
    // Clear sheetData for a specific sheet while preserving other metadata
    const tableState = states.value.find((s) => s.componentName === 'table')
    if (!tableState || !tableState.sheetData || !tableState.sheetData[sheetKey]) {
      return
    }

    this.$patch((s) => {
      const tableComp = s.states.find((state) => state.componentName === 'table')
      if (tableComp && tableComp.sheetData && tableComp.sheetData[sheetKey]) {
        // Clear only the data-related properties, preserve metadata
        const sheetMeta = tableComp.sheetData[sheetKey]
        tableComp.sheetData[sheetKey] = {
          ...sheetMeta,
          columns: [],
          rows: [],
          // Preserve table_id, name, and other metadata
          table_id: sheetMeta.table_id,
          name: sheetMeta.name,
          semanticsConfig: sheetMeta.semanticsConfig || {},
          // Mark as cleared for reload
          isCleared: true,
          clearTimestamp: new Date().toISOString(),
        }
      }
    })
  }

  function updateSheetData(sheetKey, newData) {
    // Update sheet data with fresh data from reload
    const tableState = states.value.find((s) => s.componentName === 'table')
    if (!tableState) {
      return
    }

    this.$patch((s) => {
      const tableComp = s.states.find((state) => state.componentName === 'table')
      if (tableComp) {
        const existingSheet = tableComp.sheetData?.[sheetKey] || {}

        // Merge new data with existing metadata
        tableComp.sheetData = {
          ...(tableComp.sheetData || {}),
          [sheetKey]: {
            ...existingSheet,
            ...newData,
            // Ensure table_id is preserved/updated
            table_id: newData.table_id || existingSheet.table_id,
            // Remove cleared flag
            isCleared: false,
            clearTimestamp: undefined,
          },
        }
      }
    })
  }

  function getTableId(sheetKey) {
    // Get table_id for a specific sheet
    const tableState = states.value.find((s) => s.componentName === 'table')
    if (!tableState || !tableState.sheetData || !tableState.sheetData[sheetKey]) {
      return null
    }
    return tableState.sheetData[sheetKey].table_id || null
  }

  return {
    pageName,
    title,
    taskName,
    setTaskName,
    clearTaskName,
    taskId,
    setTaskId,
    clearTaskId,
    states,
    stateIds,
    getStates,
    updateComponent,
    deleteComponent,
    chatMessages,
    setChatMessages,
    appendChatMessage,
    clearChatMessages,
    username,
    setUsername,
    clearUsername,
    userRole,
    setUserRole,
    clearUserRole,
    setAuthToken,
    clearAuthToken,
    tokenExpireTime,
    setTokenExpireTime,
    clearTokenExpireTime,
    userInfo,
    setUserInfo,
    clearUserInfo,
    clearAllAuthData,
    availableIndicators,
    setAvailableIndicators,
    clearAvailableIndicators,
    availableDimensions,
    setAvailableDimensions,
    clearAvailableDimensions,
    availableCategories,
    setAvailableCategories,
    clearAvailableCategories,
    selectedCategories,
    setSelectedCategories,
    clearSelectedCategories,
    systemError,
    showSystemError,
    hideSystemError,
    clearSystemError,
    clearSheetData,
    updateSheetData,
    getTableId,
  }
})
