# Table Data Reload Feature Implementation

## Overview
This document describes the implementation of the table data reload feature that automatically clears previous table data and reloads fresh data when switching between tables in the application.

## Core Functionality

### 1. Automatic Reload on Table Switch
- When switching from one table to another, the system automatically:
  - Clears the previous table's data from memory (if it has a `table_id`)
  - Preserves table metadata and configuration
  - Loads fresh data for the new table using its `table_id`

### 2. Manual Reload Button
- Added a green reload button (🔄) to table tabs for API-sourced tables
- But<PERSON> only appears for tables that have a `table_id` (indicating API data source)
- Allows users to manually refresh table data

### 3. Data Management
- `table_id` is stored in the centralized states.js store for each table
- `sheetData` is cleared while preserving other table properties
- Fresh data is fetched using the `/reload_data` POST endpoint

## Implementation Details

### Files Modified/Created

#### 1. `src/utils/tableReloadApi.js` (NEW)
- **`reloadTableData(table_id)`**: Calls the `/reload_data` POST endpoint
- **`transformReloadedData(apiResponse, table_id)`**: Transforms API response to internal format
- Comprehensive error handling for various failure scenarios
- Consistent error messaging and retry mechanisms

#### 2. `src/store/states.js` (MODIFIED)
- **`clearSheetData(sheetKey)`**: Clears data while preserving metadata
- **`updateSheetData(sheetKey, newData)`**: Updates sheet with fresh data
- **`getTableId(sheetKey)`**: Retrieves table_id for a specific sheet
- Enhanced table state management functions

#### 3. `src/components/DataSheet.vue` (MODIFIED)
- **Enhanced `selectSheet(key)` function**: Implements automatic reload logic
- **`reloadSheetData(sheetKey)`**: Manual reload function for reload button
- **`canReloadCurrentSheet`**: Computed property to check reload capability
- Added reload button to sheet actions UI
- CSS styles for reload button

#### 4. `src/utils/tableReloadApi.test.js` (NEW)
- Test functions for data transformation
- Error handling tests
- Manual testing instructions

## API Specification

### Endpoint: POST `/api/v1/sessions/reload_data`
```json
Request Body:
{
  "table_id": "string"
}

Response (Success):
{
  "error_code": 0,
  "message": "",
  "request_id": "string",
  "data": {
    "table_id": "string",
    "table_info": {
      "column_name": {
        "dtype": "string",
        "ftype": "categorical",
        "missing_count": 0,
        "outlier_count": 0,
        "unique_values": []
      }
    },
    "table_data": {
      "column_name": ["value1", "value2", ...]
    }
  }
}

Error Responses:
- 400: Invalid table_id
- 401: Authentication failed
- 404: Table not found
- 500: Internal server error
```

## User Experience

### Automatic Reload Flow
1. User switches from Table A to Table B
2. System clears Table A's data (if it has table_id)
3. System loads fresh data for Table B
4. Toast notifications show progress and results

### Manual Reload Flow
1. User hovers over a table tab with API data
2. Green reload button appears in sheet actions
3. User clicks reload button
4. System clears current data and fetches fresh data
5. Toast notifications show progress and results

### Error Handling
- Network errors: "网络连接失败，请检查网络连接"
- Invalid table_id: "无效的表格ID，请检查表格是否存在"
- Server errors: "服务器内部错误，请稍后重试"
- Authentication errors: "认证失败，请重新登录"

## Data Structure

### Table Data Storage
```javascript
sheetData: {
  "table_key": {
    name: "Table Name",
    columns: [...],
    rows: [...],
    table_id: "api_table_123",  // Key field for reload capability
    table_info: {...},          // Metadata from API
    table_data: {...},          // Original column-based data
    reloadTimestamp: "2024-01-01T00:00:00.000Z",
    source: "api_reload",       // Indicates data source
    isCleared: false,           // Flag for cleared state
    semanticsConfig: {...}      // Preserved configuration
  }
}
```

## Testing

### Automated Tests
- Data transformation validation
- Error handling verification
- Structure integrity checks

### Manual Testing Scenarios
1. **Table Switch Reload**: Switch between tables and verify automatic reload
2. **Manual Reload Button**: Test reload button functionality
3. **Error Handling**: Test with invalid table_id and network failures
4. **UI Behavior**: Verify button visibility and loading states
5. **Data Integrity**: Confirm data accuracy after reload

## Benefits

1. **Fresh Data**: Always displays current data from the backend
2. **Memory Efficiency**: Clears unused table data to free memory
3. **User Control**: Manual reload option for user-initiated refresh
4. **Error Resilience**: Comprehensive error handling and user feedback
5. **Seamless UX**: Automatic reload with progress indicators

## API Response Structure Fix

**Issue Resolved**: The initial implementation expected the API response to have `table_data` and `table_info` at the root level, but the actual API returns a nested structure:

```json
{
  "error_code": 0,
  "message": "",
  "data": {
    "table_id": "...",
    "table_data": {...},
    "table_info": {...}
  }
}
```

**Solution**: Updated `reloadTableData` and `transformReloadedData` functions to:
- Check for `error_code` in the response
- Extract data from `response.data.data` instead of `response.data`
- Handle the nested response structure properly
- Add comprehensive logging for debugging

## Future Enhancements

1. **Caching Strategy**: Implement intelligent caching to reduce API calls
2. **Batch Reload**: Allow reloading multiple tables simultaneously
3. **Auto-refresh**: Periodic automatic refresh for real-time data
4. **Conflict Resolution**: Handle data conflicts during concurrent edits
5. **Performance Optimization**: Lazy loading and pagination for large datasets

## Configuration

The reload feature is automatically enabled for tables with `table_id`. No additional configuration is required. The feature respects the existing authentication and error handling patterns in the application.
