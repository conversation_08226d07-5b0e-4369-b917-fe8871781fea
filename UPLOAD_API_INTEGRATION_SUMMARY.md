# File Upload API Integration Summary

## Overview
Successfully upgraded the file upload feature in the Vue.js application to integrate with the new `upload_data` API endpoint (`POST /api/v1/config/upload_data`). The implementation follows established patterns in the codebase and provides enhanced error handling and user feedback.

## Changes Made

### 1. Enhanced `src/composables/useSheetImport.js`

#### New Functions Added:
- **`uploadFileToAPI(file, taskId)`**: Handles multipart/form-data upload to the new API endpoint
  - Validates inputs and authentication token
  - Creates FormData with file and task_id parameters
  - Implements comprehensive error handling for API error codes (301, 302) and HTTP status codes
  - Returns structured response with table_id and table_data

- **`generateTaskId()`**: Creates unique task identifiers
  - Uses existing taskName from centralized state
  - Combines task name with timestamp for uniqueness
  - Format: `{taskName}_{timestamp}`

- **`storeUploadedData(table_id, table_data, fileName)`**: Stores API response data in centralized state
  - Transforms column-based API data to row-based format (following existing patterns)
  - Creates proper column metadata structure
  - Stores in Pinia state with enhanced metadata including upload timestamp and source marking

#### Key Features:
- **Authentication**: Uses token from localStorage in headers
- **Timeout**: 60-second timeout for file uploads
- **Validation**: Input validation for file, taskId, and token
- **Error Handling**: Comprehensive error classification and user-friendly messages
- **Data Transformation**: Converts API response format to match existing data structure

### 2. Updated `src/components/LeftColumn.vue`

#### File Upload Handler Enhancement:
- **Pre-upload Validation**:
  - File type validation (CSV, Excel formats only)
  - File size validation (max 10MB)
  - Prevents multiple simultaneous uploads

- **Upload Process**:
  - Generates task_id using existing taskName
  - Calls new API endpoint instead of local file parsing
  - Stores API response data in centralized state
  - Maintains existing emit patterns for parent component communication

- **Visual Feedback**:
  - Upload state management with `isUploading` reactive variable
  - Dynamic upload icon (paperclip → spinner during upload)
  - Disabled state during upload to prevent multiple requests
  - Enhanced tooltips and visual indicators

#### UI Improvements:
- **Loading States**: Visual spinner animation during upload
- **Error Prevention**: Disabled input during upload process
- **File Input Reset**: Clears input after upload to allow re-uploading same file

### 3. Enhanced `src/views/InductiveView.vue`

#### Improved User Feedback:
- **Success Messages**: Enhanced with table_id information when available
- **Error Handling**: Specific error messages for common failure scenarios:
  - Authentication failures → "请重新登录后再试"
  - Parameter errors → "请求参数缺失，请稍后重试"
  - Network errors → "请检查网络连接后重试"
  - Server errors → "服务器暂时不可用，请稍后重试"

### 4. CSS Enhancements

#### Upload State Styling:
- **`.uploading` class**: Visual feedback for upload in progress
- **Spinner animation**: Smooth rotation animation for loading indicator
- **Hover states**: Disabled hover effects during upload
- **Opacity changes**: Visual indication of disabled state

## API Integration Details

### Request Format:
```
POST /api/v1/config/upload_data
Content-Type: multipart/form-data
Headers: 
  - token: {authentication_token}

Form Data:
  - file: {uploaded_file}
  - task_id: {generated_task_id}
```

### Response Handling:
- **Success (error_code: 0)**: Extract table_id and table_data from response.data
- **Error (error_code: 301)**: Authentication failure → redirect to login
- **Error (error_code: 302)**: Missing parameters → user guidance
- **HTTP Errors**: Comprehensive status code handling (400, 401, 413, 415, etc.)

### Data Storage Pattern:
- Follows existing centralized state management in `states.js`
- Transforms column-based API data to row-based format for UI compatibility
- Maintains metadata structure consistent with existing data sheets
- Marks uploaded data with source identifier for tracking

## Error Handling Strategy

### Three-Layer Error Handling:
1. **Input Validation**: Pre-upload checks (file type, size, authentication)
2. **API Error Codes**: Specific handling for business logic errors (301, 302)
3. **HTTP Status Codes**: Network and server error handling

### User Experience:
- **Progressive Enhancement**: Maintains existing functionality as fallback
- **Clear Messaging**: User-friendly error messages with actionable guidance
- **Visual Feedback**: Loading states and progress indicators
- **Error Recovery**: Allows retry after failures

## Backward Compatibility
- Maintains existing `importAndAddSheet` function for local file parsing
- Preserves all existing emit patterns and component interfaces
- Data structure remains compatible with existing DataSheet component
- No breaking changes to parent-child component communication

## Testing Recommendations
1. **File Upload Flow**: Test with various file types and sizes
2. **Error Scenarios**: Test authentication failures, network errors, invalid files
3. **UI States**: Verify loading states, error messages, success feedback
4. **Data Integration**: Confirm uploaded data appears correctly in DataSheet
5. **Edge Cases**: Test simultaneous uploads, large files, network interruptions

## Security Considerations
- Authentication token validation before upload
- File type and size restrictions
- Input sanitization and validation
- Timeout protection against hanging requests
